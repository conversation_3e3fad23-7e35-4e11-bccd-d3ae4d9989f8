#!/usr/bin/env python3
"""
Frontend Superpowers Training Script (Fixed)
Adds HTML/CSS generation capabilities to the existing Qwen3-8B Python coder
"""

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, TrainingArguments, Trainer, DataCollatorForLanguageModeling
from peft import PeftModel, LoraConfig, get_peft_model, prepare_model_for_kbit_training
from datasets import load_dataset
import json
from datetime import datetime

def load_existing_model():
    """Load the existing Python coder model"""
    print("🔄 Loading existing Qwen3-8B Python coder...")
    
    # Load base model
    base_model = AutoModelForCausalLM.from_pretrained(
        "Qwen/Qwen3-8B",
        torch_dtype=torch.float16,
        device_map="auto",
        trust_remote_code=True
    )
    
    tokenizer = AutoTokenizer.from_pretrained(
        "Qwen/Qwen3-8B",
        trust_remote_code=True
    )
    
    # Add padding token if not present
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Load existing Python coder adapter
    model = PeftModel.from_pretrained(
        base_model,
        "./qwen-python-coder",
        torch_dtype=torch.float16
    )
    
    print("✅ Existing Python coder loaded!")
    return model, tokenizer

def prepare_frontend_dataset():
    """Prepare the frontend dataset for training"""
    print("🔄 Loading frontend dataset...")
    
    # Load dataset
    ds = load_dataset('marianna13/frontend-instruction-tuning')
    
    # Filter out samples with null data
    def filter_valid_samples(example):
        json_data = example['json']
        return (json_data['html'] and json_data['css'] and 
                json_data['html'] != 'None' and json_data['css'] != 'None' and
                len(json_data['html']) > 10 and len(json_data['css']) > 10)
    
    filtered_ds = ds['train'].filter(filter_valid_samples)
    print(f"✅ Filtered dataset: {len(filtered_ds)} valid samples")
    
    return filtered_ds

def format_frontend_data(example):
    """Format a single frontend data example for instruction tuning"""
    json_data = example['json']
    html = json_data['html']
    css = json_data['css']
    
    # Create instruction-response format
    instruction = "Generate HTML and CSS code for a frontend component."
    response = f"HTML:\n{html}\n\nCSS:\n{css}"
    
    formatted_text = f"### Instruction:\n{instruction}\n\n### Response:\n{response}<|endoftext|>"
    
    return {"text": formatted_text}

def tokenize_function(examples, tokenizer):
    """Tokenize the formatted text"""
    return tokenizer(
        examples["text"],
        truncation=True,
        padding=False,
        max_length=2048,
        return_overflowing_tokens=False,
    )

def setup_training():
    """Set up training configuration"""
    print("🔄 Setting up training configuration...")
    
    # Training arguments
    training_args = TrainingArguments(
        output_dir="./qwen-fullstack-coder",
        num_train_epochs=2,
        per_device_train_batch_size=1,
        gradient_accumulation_steps=8,
        warmup_steps=100,
        learning_rate=2e-4,
        fp16=True,
        logging_steps=10,
        save_steps=500,
        evaluation_strategy="no",
        save_strategy="steps",
        load_best_model_at_end=False,
        report_to=None,
        remove_unused_columns=False,
        dataloader_drop_last=True,
    )
    
    return training_args

def main():
    """Main training function"""
    print("🚀 Starting Frontend Superpowers Training!")
    print("=" * 80)
    
    # Load existing model
    model, tokenizer = load_existing_model()
    
    # Prepare dataset
    dataset = prepare_frontend_dataset()
    
    # Format dataset
    print("🔄 Formatting dataset...")
    formatted_dataset = dataset.map(
        format_frontend_data,
        remove_columns=dataset.column_names
    )
    
    # Tokenize dataset
    print("🔄 Tokenizing dataset...")
    tokenized_dataset = formatted_dataset.map(
        lambda examples: tokenize_function(examples, tokenizer),
        batched=True
    )
    
    # Setup training
    training_args = setup_training()
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # Create trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator,
    )
    
    print("🎯 Starting training...")
    print(f"📊 Training samples: {len(tokenized_dataset)}")
    print(f"🔥 Training will add frontend superpowers to your Python coder!")
    
    # Start training
    trainer.train()
    
    # Save the enhanced model
    print("💾 Saving full-stack coder model...")
    trainer.save_model("./qwen-fullstack-coder")
    tokenizer.save_pretrained("./qwen-fullstack-coder")
    
    print("🎉 Frontend superpowers training completed!")
    print("Your model can now generate both Python code AND HTML/CSS!")

if __name__ == "__main__":
    main()
